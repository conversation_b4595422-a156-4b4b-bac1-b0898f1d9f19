import pandas as pd
import sys

def view_excel_content(file_path):
    """查看Excel文件内容"""
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        print(f"表格文件: {file_path}")
        print(f"数据形状: {df.shape[0]} 行 x {df.shape[1]} 列")
        print("\n列名:")
        for i, col in enumerate(df.columns):
            print(f"{i+1}. {col}")
        
        print("\n前10行数据:")
        print(df.head(10).to_string(index=True))
        
        print("\n数据类型:")
        print(df.dtypes)
        
        print("\n基本统计信息:")
        print(df.describe(include='all'))
        
        # 检查是否有缺失值
        missing_values = df.isnull().sum()
        if missing_values.any():
            print("\n缺失值统计:")
            print(missing_values[missing_values > 0])
        else:
            print("\n没有缺失值")
            
    except Exception as e:
        print(f"读取文件时出错: {e}")

if __name__ == "__main__":
    file_path = "大众点评网.xlsx"
    view_excel_content(file_path)
